package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 案件处理审批表文档生成实现类
 *
 * <AUTHOR>
 */
@Service("caseApprovalFormDocument")
public class CaseApprovalDocumentImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
		processedData = StrUtil.isNotBlank(caseId) ? getData(caseId) : getMockData();
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "36案件处理审批表_个人.docx";//todo 如何区分个人和单位
    }

    @Override
    public String getDocumentType() {
        return "CASE-APPROVAL";
    }

	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> query=new HashMap<>();
//		query.put("AJBS", "008a2ca3592c4f8688a696b16736003a");
		query.put("AJBS", caseId);
//		query.put("PAGESIZE", 20);

		JSONArray array = icaseInfoService.getCaseHandleApprovalDailyReport(query);
		if(array != null && !array.isEmpty()) {
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			Map<String, String> mapper = getTableFieldMapping();
			if(firstData != null) {
				// 处理数据
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					data.put(newKey, value);
				});
				System.out.println(data);
				return data;
			}
		}
		return data;
	}

	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMapping = new HashMap<>();

		fieldMapping.put("JGSXZ", "org_abbr");
		fieldMapping.put("CBRYJ", "handle_person_advice");
		fieldMapping.put("AJBS", "case_uuid");
		fieldMapping.put("CJR", "creator");
		fieldMapping.put("SFLX", "is_type");
		fieldMapping.put("AJBH", "case_code");
		fieldMapping.put("JGJC", "org_shortname");
		fieldMapping.put("CBRUUID", "handle_person_uuids");
		fieldMapping.put("KZZD2", "ext2");
		fieldMapping.put("DSRXM", "party");
		fieldMapping.put("CLSPBS", "handle_uuid");
		fieldMapping.put("LARQ", "reg_date");
		fieldMapping.put("ZZ", "home_addr");
		fieldMapping.put("LABH", "reg_no");
		fieldMapping.put("CHBMUUID", "get_dept_uuid");
		fieldMapping.put("QYMC", "company_name");
		fieldMapping.put("CBBMUUID", "reg_dept_uuid");
		fieldMapping.put("FZRYJ", "apv_pic_advice");
		fieldMapping.put("SJMC", "city_org_name");
		fieldMapping.put("AJSS", "case_fact");
		fieldMapping.put("DSRXB", "party_sex");
		fieldMapping.put("XGR", "modifier");
		fieldMapping.put("FZFZR", "legal_manager");
		fieldMapping.put("BMFZR", "dept_manager");
		fieldMapping.put("FZR", "manager_name");
		fieldMapping.put("ZZJGUUID", "org_uuid");
		fieldMapping.put("FDDBR", "pic");
		fieldMapping.put("AJLY", "case_source_clues");
		fieldMapping.put("BZ", "remark");
		fieldMapping.put("KZZD1", "ext1");
		fieldMapping.put("SFYX", "is_active");
		fieldMapping.put("ZY", "vocation");
		fieldMapping.put("CJSJ", "create_time");
		fieldMapping.put("FZSHSJ", "legal_audit_date");
		fieldMapping.put("KZZD3", "ext3");
		fieldMapping.put("SJBM", "city_org_code");
		fieldMapping.put("CFYJ", "punish_argument");
		fieldMapping.put("ZMBMYJSJ", "r_dept_date");
		fieldMapping.put("JYDZ", "busi_address");
		fieldMapping.put("QYLXDH", "com_phone");
		fieldMapping.put("AY", "cause_of_action");
		fieldMapping.put("XGSJ", "modify_time");
		fieldMapping.put("TJSJ", "submit_time");
		fieldMapping.put("BMSHSJ", "dept_audit_date");
		fieldMapping.put("XTGXSJ", "sys_modify_time");
		fieldMapping.put("FZSHYJ", "legal_advice");
		fieldMapping.put("TAR", "case_partner");
		fieldMapping.put("ZJHM", "id_card");
		fieldMapping.put("ZMBMFZR", "r_dept_manager");
		fieldMapping.put("ZMBMYJ", "r_dept_advice");
		fieldMapping.put("NL", "age");
		fieldMapping.put("FWZXSJGXSJ", "sysupdatetime");
		fieldMapping.put("SFYS", "is_trans");
		fieldMapping.put("CBR", "handle_person");
		fieldMapping.put("SJSSBM", "own_dept_uuid");
		fieldMapping.put("FZRSPSJ", "apv_pic_date");
		fieldMapping.put("XTCJSJ", "sys_create_time");
		fieldMapping.put("FWZXSJSCBJ", "sysisdelete");
		fieldMapping.put("XYWYBS", "tid");
		fieldMapping.put("MCRKSJ", "mc_tec_ctime");
		fieldMapping.put("AJMC", "case_name");
		fieldMapping.put("SJSSDW", "own_org_uuid");
		fieldMapping.put("CSRQ", "birthday");
		fieldMapping.put("SFZSFJMS", "is_branch");
		fieldMapping.put("ZW", "duties");
		fieldMapping.put("WCZT", "finsh_status");
		fieldMapping.put("CBBMYJ", "dept_advice");
		fieldMapping.put("MZ", "nation");
		fieldMapping.put("TART", "same_party");
		fieldMapping.put("ZJLX", "id_card_type");
		fieldMapping.put("LXDH", "contact_phone");

		return fieldMapping;
	}

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("approval_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_name", "广东省博罗县烟草专卖局");
        mockData.put("case_no", "博烟案﹝2025﹞第48号");
        mockData.put("approval_date", "2025/6/10");

        // 案件基本信息
        mockData.put("case_name", "梁俊强未在当地烟草专卖批发企业进货案");
        mockData.put("case_type", "行政处罚案件");
        mockData.put("case_level", "一般案件");
        mockData.put("case_source", "群众举报");
        mockData.put("register_date", "2025/3/18");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("party_type", "个体工商户");
        mockData.put("business_name", "博罗县龙溪隆胜轩茶烟酒商行");
        mockData.put("license_no", "************");
        mockData.put("party_address", "广东省博罗县龙溪街道宫庭村龙桥大道1239号");

        // 违法事实概述
        mockData.put("violation_summary", "当事人梁俊强经营的博罗县龙溪隆胜轩茶烟酒商行，未在当地烟草专卖批发企业进货，现场发现涉嫌违法的烟草专卖品17个品种合计1075条，进货总额108625.00元。");

        // 初审意见
        mockData.put("initial_review_opinion", "经初步审查，当事人梁俊强的行为违反了《中华人民共和国烟草专卖法实施条例》第二十三条第二款的规定，构成未在当地烟草专卖批发企业进货的违法行为。事实清楚，证据确凿，建议依法给予行政处罚。");
        mockData.put("initial_reviewer", "叶辉明");
        mockData.put("initial_review_date", "2025/6/8");

        // 法制审核意见
        mockData.put("legal_review_opinion", "经审核，本案事实清楚，证据确凿充分，适用法律法规正确，程序合法，处罚幅度适当，符合法律法规规定。同意初审意见，建议按程序作出行政处罚决定。");
        mockData.put("legal_reviewer", "蔡秋宝");
        mockData.put("legal_review_date", "2025/6/9");

        // 拟处罚决定
        mockData.put("proposed_punishment", "依据《中华人民共和国烟草专卖法实施条例》第五十六条的规定，并遵照《广东省烟草专卖行政处罚裁量权管理办法》第十一条第（三）项及附件1的处罚幅度，对当事人作出从重处罚决定：处以未在当地烟草专卖批发企业进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元，上缴国库。");

        // 处罚金额
        mockData.put("fine_amount", 10319.37);
        mockData.put("confiscate_amount", 0.00);
        mockData.put("total_amount", 10319.37);

        // 审批意见
        mockData.put("approval_opinion", "同意法制审核意见和初审意见，批准按拟定的处罚决定执行。");
        mockData.put("approval_result", "批准");

        // 审批人信息
        mockData.put("approver_name", "张局长");
        mockData.put("approver_position", "局长");
        mockData.put("approver_signature", "");
        mockData.put("approval_final_date", "2025/6/10");

        // 执行情况
        mockData.put("execution_status", "待执行");
        mockData.put("execution_note", "批准后制作行政处罚决定书并送达当事人");

        // 案件流转记录
        mockData.put("case_flow", "立案 → 调查取证 → 初审 → 法制审核 → 审批 → 制作决定书");

        // 办案人员
        mockData.put("investigator1", "叶辉明");
        mockData.put("investigator1_id", "19090352015");
        mockData.put("investigator2", "朱兆强");
        mockData.put("investigator2_id", "19090352023");

        // 案件统计信息
        mockData.put("case_duration", "84天");
        mockData.put("evidence_count", "7项");
        mockData.put("hearing_requested", "否");

        // 备注信息
        mockData.put("remarks", "当事人为三次以上实施涉烟违法行为，依法从重处罚。");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 16:00");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 16:00");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");

        return mockData;
    }
}
