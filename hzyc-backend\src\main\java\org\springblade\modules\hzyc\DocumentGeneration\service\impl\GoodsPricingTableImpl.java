package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 涉案物品核价表文档生成实现类
 *
 * <AUTHOR>
 */
@Service("goodsPricingTable")
public class GoodsPricingTableImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
        processedData = getMockData(); //todo，后续改为读取省局接口
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "18涉案物品核价表.docx";
    }

    @Override
    public String getDocumentType() {
        return "GOODS-PRICING-TABLE";
    }

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("pricing_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟价﹝2025﹞第48号");
        mockData.put("doc_date", "2025年6月10日");

        // 案件信息
        mockData.put("case_name", "梁俊强涉嫌违法经营卷烟案");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("business_name", "博罗县龙溪隆胜轩茶烟酒商行");

        // 核价信息
        mockData.put("pricing_time", "2025年03月20日");
        mockData.put("pricing_basis", "根据国家烟草专卖局发布的卷烟价格目录及市场调研价格");
        mockData.put("pricing_method", "按照卷烟建议零售价格核定");

        // 涉案物品清单
        List<Map<String, Object>> goodsItems = new ArrayList<>();

        Map<String, Object> item1 = new HashMap<>();
        item1.put("order_index", 1);
        item1.put("goods_name", "黄果树(长征)");
        item1.put("spec", "20支/包，10包/条");
        item1.put("unit", "条");
        item1.put("qty", 200.0);
        item1.put("unit_price", 100.0);
        item1.put("total_amt", 20000.0);
        item1.put("price_source", "国家烟草专卖局价格目录");
        item1.put("memo", "建议零售价100元/条");
        goodsItems.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("order_index", 2);
        item2.put("goods_name", "白沙(硬精品三代)");
        item2.put("spec", "20支/包，10包/条");
        item2.put("unit", "条");
        item2.put("qty", 150.0);
        item2.put("unit_price", 120.0);
        item2.put("total_amt", 18000.0);
        item2.put("price_source", "国家烟草专卖局价格目录");
        item2.put("memo", "建议零售价120元/条");
        goodsItems.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("order_index", 3);
        item3.put("goods_name", "红塔山(硬经典)");
        item3.put("spec", "20支/包，10包/条");
        item3.put("unit", "条");
        item3.put("qty", 150.0);
        item3.put("unit_price", 80.0);
        item3.put("total_amt", 12000.0);
        item3.put("price_source", "国家烟草专卖局价格目录");
        item3.put("memo", "建议零售价80元/条");
        goodsItems.add(item3);

        Map<String, Object> item4 = new HashMap<>();
        item4.put("order_index", 4);
        item4.put("goods_name", "黄山(新一品)");
        item4.put("spec", "20支/包，10包/条");
        item4.put("unit", "条");
        item4.put("qty", 100.0);
        item4.put("unit_price", 150.0);
        item4.put("total_amt", 15000.0);
        item4.put("price_source", "国家烟草专卖局价格目录");
        item4.put("memo", "建议零售价150元/条");
        goodsItems.add(item4);

        Map<String, Object> item5 = new HashMap<>();
        item5.put("order_index", 5);
        item5.put("goods_name", "其他品牌卷烟");
        item5.put("spec", "各种规格");
        item5.put("unit", "条");
        item5.put("qty", 475.0);
        item5.put("unit_price", 91.32);
        item5.put("total_amt", 43625.0);
        item5.put("price_source", "国家烟草专卖局价格目录");
        item5.put("memo", "多个品牌平均价格");
        goodsItems.add(item5);

        mockData.put("goods_items", goodsItems);

        // 统计信息
        mockData.put("total_goods_count", 5);
        mockData.put("total_qty", 1075.0);
        mockData.put("total_amt", 108625.0);
        mockData.put("avg_unit_price", 101.05);

        // 核价说明
        mockData.put("pricing_desc", "本次核价严格按照国家烟草专卖局发布的卷烟建议零售价格执行，确保价格的准确性和权威性。");

        // 核价人员
        mockData.put("pricing_person", "叶辉明");
        mockData.put("pricing_person_insp_no", "19090352015");
        mockData.put("reviewer", "朱兆强");
        mockData.put("reviewer_insp_no", "19090352023");

        // 法律依据
        mockData.put("legal_basis", "《烟草专卖行政处罚程序规定》第二十条、《涉案物品价格认定管理办法》");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");

        return mockData;
    }
}
