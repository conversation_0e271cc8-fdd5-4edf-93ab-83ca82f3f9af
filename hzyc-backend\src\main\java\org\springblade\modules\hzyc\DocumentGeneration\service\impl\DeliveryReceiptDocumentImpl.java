package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
/**
 * 送达回证文档生成实现类
 *
 * <AUTHOR>
 */
@Service("deliveryReceiptDocument")
public class DeliveryReceiptDocumentImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);

        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
        processedData = StrUtil.isNotBlank(caseId) ? getData(caseId) : getMockData();

        // 处理service_content，如果是多条记录，需要为每条记录生成单独的数据
        String serviceContentStr = (String) processedData.get("service_content");
        if (serviceContentStr != null && serviceContentStr.startsWith("[")) {
            try {
                List<Map<String, Object>> serviceContents = JsonUtil.readListMap(serviceContentStr);
                if (serviceContents.size() > 1) {
                    // 为每条记录生成单独的数据
                    List<Map<String, Object>> resultList = new ArrayList<>();
                    for (Map<String, Object> content : serviceContents) {
                        Map<String, Object> docData = new HashMap<>(processedData);
                        docData.put("doc_name", content.get("docName"));
                        docData.put("doc_no", content.get("docNo"));
                        docData.put("modify_time",formatDateString(String.valueOf(processedData.get("modify_time")) ) );
                        resultList.add(docData);
                    }
                    return resultList;
                } else if (serviceContents.size() == 1) {
                    // 只有一条记录
                    Map<String, Object> content = serviceContents.get(0);
                    processedData.put("doc_name", content.get("docName"));
                    processedData.put("doc_no", content.get("docNo"));
                    processedData.put("modify_time",formatDateString(String.valueOf(processedData.get("modify_time")) ) );
                }
            } catch (Exception e) {
                // 如果解析失败，保持原值
            }
        }

        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "45送达回证.docx";
    }

    @Override
    public String getDocumentType() {
        return "DELIVERY-RECEIPT";
    }

	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMap = new HashMap<>();

		// 映射字段
		fieldMap.put("FJID", "file_uuids");
		fieldMap.put("SDNRSDWSMCBH", "service_content");
		fieldMap.put("WSRQ", "doc_date");
		fieldMap.put("DSR", "party");
		fieldMap.put("SDNRJSONZFC", "delivery_json");
		fieldMap.put("SDR", "to_receive_person");
		fieldMap.put("CJSJ", "create_time");
		fieldMap.put("SDHZBS", "service_receipt_uuid");
		fieldMap.put("WSSDFS", "doc_send_type");
		fieldMap.put("SFQY", "is_active");
		fieldMap.put("SSRQZ", "receiver_name");
		fieldMap.put("SQJLUUID", "handle_main_uuid");
		fieldMap.put("SFJSDZSD", "is_accept_elec_delivery");
		fieldMap.put("XTGXSJCXBYDX", "sys_modify_time");
		fieldMap.put("SDRID", "to_receive_person_uuid");
		fieldMap.put("XTCJSJCXBYDX", "sys_create_time");
		fieldMap.put("DWJC", "org_shortname");
		fieldMap.put("SFYJXD", "is_mail_order");
		fieldMap.put("SFGGSD", "is_announce_service");
		fieldMap.put("AJUUID", "case_uuid");
		fieldMap.put("TAR", "same_party");
		fieldMap.put("SSDRLXDH", "receive_contact_tel");
		fieldMap.put("XGR", "modifier");
		fieldMap.put("CJR", "creator");
		fieldMap.put("BZ", "remark");
		fieldMap.put("SDFSBCSM", "service_style_added");
		fieldMap.put("SDHZWSLX", "service_receipt_type");
		fieldMap.put("XFLSH", "ems_order_no");
		fieldMap.put("WSH", "doc_no");
		fieldMap.put("YWXSXWSMXBLSID", "lidd_id");
		fieldMap.put("ZZJGUUID", "org_uuid");
		fieldMap.put("AJMC", "case_name");
		fieldMap.put("ZMRQZRQ", "witness_date");
		fieldMap.put("DWSXZ", "org_abbr");
		fieldMap.put("CBBMUUID", "reg_dept_uuid");
		fieldMap.put("AJJBXXBJGJC", "org_short_name_basic");
		fieldMap.put("WORDWSLJ", "word_file_path");
		fieldMap.put("SSDR", "receive_person");
		fieldMap.put("ZMRQZ", "witness_name");
		fieldMap.put("XYWYBS", "tid");
		fieldMap.put("SDDD", "service_addr");
		fieldMap.put("SJBM", "city_org_code");
		fieldMap.put("ND", "doc_year");
		fieldMap.put("SSRQZRQ", "receiver_date");
		fieldMap.put("WCZT", "finsh_status");
		fieldMap.put("SJMC", "city_org_name");
		fieldMap.put("AJBH", "case_code");
		fieldMap.put("MCRKSJ", "mc_tec_ctime");
		fieldMap.put("WSHQ", "full_doc_no");
		fieldMap.put("AJJBXXBJGSXZ", "org_abbr_basic");
		fieldMap.put("QSRQ", "service_date");
		fieldMap.put("CHBMUUID", "get_dept_uuid");
		fieldMap.put("XGSJ", "modify_time");

		return fieldMap;
	}

	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> query=new HashMap<>();
//		query.put("AJUUID", caseId);
		query.put("AJUUID", "0171bb1f7b774b7d956825a97702aba6");
//		query.put("PAGESIZE", 20);

		JSONArray array = icaseInfoService.getCaseDeliveryReceiptDailyReport(query);
		if(array != null && !array.isEmpty()) {
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			Map<String, String> mapper = getTableFieldMapping();
			if(firstData != null) {
				// 处理数据
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					data.put(newKey, value);
				});
				System.out.println(data);
				return data;
			}
		}
		return data;
	}

	/**
     * 获取模拟数据
     */
     private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();
        // 基础信息
        mockData.put("service_receipt_uuid", "***********-001");
        mockData.put("handle_main_uuid", "***********-001");
        mockData.put("receive_person", "梁俊强");
        mockData.put("to_receive_person", "梁俊强");
        mockData.put("to_receive_person_uuid", "TRP-10086");
        mockData.put("service_addr", "广东省博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("service_date", "2025-06-10");
        mockData.put("service_style_added", "直接送达");
        mockData.put("service_content", "[{\"docUuid\":\"222894debb464e6badf3b1f07e41f6a1\",\"docName\":\"烟草专卖零售许可证准予行政许可决定书\",\"docNo\":\"惠阳烟专延〔2024〕许第1364号\"},{\"docUuid\":\"2101fb2fc93649918e23c83a5919e803\",\"docName\":\"烟草专卖零售许可证（正、副本）\",\"docNo\":\"************\"}]");

        // 签收信息
        mockData.put("receiver_name", "梁俊强（签字）");
        mockData.put("receiver_date", "2025-06-10");
        mockData.put("witness_name", "朱兆强");
        mockData.put("witness_date", "2025-06-10");

        // 机构信息
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("org_abbr", "BLZMJ");
        mockData.put("doc_year", "2025");
        mockData.put("doc_no", "48");
        mockData.put("full_doc_no", "博烟处﹝2025﹞第48号");
        mockData.put("doc_date", "2025-06-10");

        // 状态信息
        mockData.put("finsh_status", "已完成");
        mockData.put("lidd_id", "LIDD-20250610-001");
        mockData.put("word_file_path", "/documents/2025/SR-20250610-001.docx");
        mockData.put("doc_send_type", "直接送达");
        mockData.put("file_uuids", "FILE-001,FILE-002");
        mockData.put("is_active", 1);
        mockData.put("remark", "当事人现场签收");

        // 系统字段
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025-06-10 17:15:00");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025-06-10 17:15:00");
        mockData.put("receive_contact_tel", "13640736270");
        mockData.put("sys_create_time", "2025-06-10 17:15:00");
        mockData.put("sys_modify_time", "2025-06-10 17:15:00");

        // 扩展字段
        mockData.put("is_announce_service", 0);
        mockData.put("service_receipt_type", "行政处罚");
        mockData.put("is_accept_elec_delivery", 1);
        mockData.put("delivery_json", "{\"doc_type\":\"行政处罚决定书\",\"doc_number\":\"博烟处﹝2025﹞第48号\"}");
        mockData.put("tid", "TID-20250610-001");
        mockData.put("is_mail_order", 0);
        mockData.put("ems_order_no", "");
        mockData.put("city_org_code", "441322");
        mockData.put("city_org_name", "惠州市博罗县");
        mockData.put("mc_tec_ctime", "2025-06-10 17:15:00");

        return mockData;
    }
        /**
     * 将YYYY-MM-DD格式的日期转换为YYYY年MM月DD日格式
     */
    private String formatDateString(String dateStr) {
        if (dateStr == null || dateStr.equals("-")) {
            return "";
        }

        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月dd日");
            Date date = inputFormat.parse(dateStr);
            return outputFormat.format(date);
        } catch (Exception e) {
            return dateStr;
        }
    }

}
